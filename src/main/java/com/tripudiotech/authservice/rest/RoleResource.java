/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.rest;

import com.tripudiotech.authservice.request.CreateRoleRequest;
import com.tripudiotech.authservice.response.RoleResponse;
import com.tripudiotech.authservice.service.RoleService;
import com.tripudiotech.securitylib.constant.RoleConstant;
import com.tripudiotech.securitylib.dto.UserInformation;
import com.tripudiotech.securitylib.service.provider.SecurityProviderServiceFactory;
import io.smallrye.mutiny.Uni;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

@Path("/role")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@SecurityRequirement(name = "apiToken")
@Slf4j
public class RoleResource extends RestResource {

    @Inject
    RoleService roleService;

    @Inject
    SecurityProviderServiceFactory securityProviderServiceFactory;

    @GET
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
    @Operation(summary = "Getting all User Roles available in the system")
    public Uni<Response> getRoles() {
        UserInformation userInformation =
                securityProviderServiceFactory.getDefaultAuthenticateService().getCurrentUserInformation(this.tenantId);
        return roleService.getRoles(this.tenantId, userInformation)
                .map(roles -> Response.ok().entity(roles).build());
    }


    @APIResponseSchema(value = RoleResponse.class)
    @Operation(summary = "Create a new Role")
    @POST
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    public Uni<Response> createRole(@Valid CreateRoleRequest roleDto) {
        UserInformation userInformation =
                securityProviderServiceFactory.getDefaultAuthenticateService().getCurrentUserInformation(this.tenantId);
        return roleService.createRole(this.tenantId, roleDto, userInformation)
                .map(it -> {
                    if (it == null) {
                        return Response.status(Response.Status.BAD_REQUEST).build();
                    } else {
                        return Response.status(Response.Status.CREATED).entity(it).build();
                    }
                });
    }


    @APIResponseSchema(value = Void.class)
    @Operation(summary = "Delete a Role")
    @DELETE
    @Path("/{entityId}")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    public Uni<Response> deleteRole(@PathParam("entityId") String entityId) {
        return roleService.deleteRole(this.tenantId, entityId).map(result -> Response.noContent().build());
    }

}
