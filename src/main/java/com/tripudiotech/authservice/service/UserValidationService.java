/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service;

import com.tripudiotech.base.client.dto.request.AccountRequest;
import com.tripudiotech.base.configuration.exception.BadRequestException;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * Service responsible for validating user creation requests.
 * Follows Single Responsibility Principle by focusing only on validation logic.
 */
@ApplicationScoped
@Slf4j
public class UserValidationService {

    @Inject
    Validator validator;

    /**
     * Validates the account request using Bean Validation annotations.
     * 
     * @param tenantId the tenant identifier for error context
     * @param request the account request to validate
     * @return Uni that completes successfully if validation passes, or fails with BadRequestException
     */
    public Uni<Void> validateAccountRequest(@NonNull String tenantId, @NonNull AccountRequest request) {
        Set<ConstraintViolation<AccountRequest>> violations = validator.validate(request);
        
        if (violations.isEmpty()) {
            return Uni.createFrom().voidItem();
        }

        String errorMessage = violations.stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        
        log.warn("Account request validation failed for tenant {}: {}", tenantId, errorMessage);
        return Uni.createFrom().failure(new BadRequestException(tenantId, errorMessage));
    }

    /**
     * Validates that the email format is correct and normalizes it.
     * 
     * @param email the email to validate and normalize
     * @return normalized email in lowercase with trimmed whitespace
     * @throws IllegalArgumentException if email is null or blank
     */
    public String validateAndNormalizeEmail(@NonNull String email) {
        if (email == null || email.trim().isEmpty()) {
            throw new IllegalArgumentException("Email cannot be null or blank");
        }
        
        String normalizedEmail = email.toLowerCase().trim();
        
        // Basic email format validation (Bean Validation should handle this, but adding as safety)
        if (!normalizedEmail.contains("@") || normalizedEmail.length() < 3) {
            throw new IllegalArgumentException("Invalid email format: " + email);
        }
        
        return normalizedEmail;
    }

    /**
     * Validates that required user information is present.
     * 
     * @param request the account request to check
     * @return true if all required fields are present
     */
    public boolean hasRequiredUserInformation(@NonNull AccountRequest request) {
        return request.getUsername() != null && !request.getUsername().trim().isEmpty() &&
               request.getFirstName() != null && !request.getFirstName().trim().isEmpty() &&
               request.getLastName() != null && !request.getLastName().trim().isEmpty();
    }
}
