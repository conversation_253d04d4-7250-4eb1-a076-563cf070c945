/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service;

import com.tripudiotech.authservice.repository.SysRootRepository;
import com.tripudiotech.base.configuration.exception.BadRequestException;
import com.tripudiotech.base.configuration.exception.PermissionDeniedException;
import com.tripudiotech.base.util.ReactorUtils;
import com.tripudiotech.datalib.db.query.RxQueryResult;
import com.tripudiotech.securitylib.constant.RoleConstant;
import com.tripudiotech.securitylib.constant.TenantConstant;
import com.tripudiotech.securitylib.dto.UserInformation;
import com.tripudiotech.securitylib.dto.request.CreateRealmRequest;
import com.tripudiotech.securitylib.dto.response.RealmResponse;
import com.tripudiotech.securitylib.service.provider.SecurityProviderServiceFactory;
import io.smallrye.mutiny.Uni;
import jakarta.annotation.security.RolesAllowed;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.ForbiddenException;
import java.util.List;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@ApplicationScoped
@Slf4j
public class TenantService {

    private static final String LOG_PREFIX = "[TenantService]";

    @Inject
    SysRootRepository sysRootRepository;

    @Inject
    SecurityProviderServiceFactory securityProviderServiceFactory;

    public RealmResponse createTenant(
            @NonNull String tenant,
            @NonNull CreateRealmRequest createTenantRequest
    ) {
        try {
            return securityProviderServiceFactory.getDefaultAuthenticateService().createRealm(tenant, createTenantRequest);
        } catch (ForbiddenException forbiddenException) {
            throw new PermissionDeniedException(tenant, forbiddenException.getMessage());
        }
    }

    @RolesAllowed({RoleConstant.ADMIN})
    public List<RealmResponse> getTenants(String tenantId) {
        return securityProviderServiceFactory.getDefaultAuthenticateService().getAllRealms(tenantId);
    }

    @RolesAllowed({RoleConstant.ADMIN})
    public Uni<Void> deleteTenant(@NonNull String tenant) {
        if (TenantConstant.MASTER_TENANT_ID.equals(tenant)) {
            throw new BadRequestException(tenant, "[Master] tenant can not be deleted");
        }
        UserInformation userInformation = securityProviderServiceFactory.getDefaultAuthenticateService().getCurrentUserInformation();
        if (!TenantConstant.MASTER_TENANT_ID.equals(userInformation.getTenantId())) {
            throw new PermissionDeniedException(userInformation.getTenantId(), "Only admin/superadmin from [master] tenant able to delete other tenant");
        }
        RxQueryResult.Total result = sysRootRepository.getAllWithPaging(tenant, null, 0, 1, userInformation.getEmail());
        Mono<List<Integer>> totalResultMono = result.getRxTotal().collectList();
        return ReactorUtils.toUni(totalResultMono).flatMap(resultTotal -> {
            int totalRecordsFound = resultTotal.stream().findFirst().orElse(0);
            if (totalRecordsFound > 0) {
                throw new BadRequestException(tenant, "Given tenant has some nodes in DB and can not be deleted");
            }
            securityProviderServiceFactory.getDefaultAuthenticateService().deleteRealm(tenant);
            return Uni.createFrom().voidItem();
        });
    }
}
